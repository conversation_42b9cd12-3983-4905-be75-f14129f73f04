<?php

namespace App\Controllers\employee;

use App\Core\Controller;

class EmployeeController extends Controller
{
    public function login()
    {
        $this->view('employee/login/login');
    }

    public function authenticate()
    {
        // Handle employee login authentication
        // TODO: Implement authentication logic
        echo "Employee authentication not implemented yet";
    }

    public function register()
    {
        echo "Register method called"; // Debugging
        $this->view('employee/register/register');
    }

    public function processRegister()
    {
        // Handle employee registration processing
        // TODO: Implement registration processing logic
        echo "Employee registration processing not implemented yet";
    }

    public function dashboard()
    {
        // Employee dashboard
        // TODO: Implement employee dashboard
        echo "Employee dashboard not implemented yet";
    }
}
