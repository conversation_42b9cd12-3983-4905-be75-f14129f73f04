<!doctype html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>Employee <PERSON>gin - State University of Northern Negros</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.0/font/bootstrap-icons.css">
    <style>
        :root {
            --sunn-primary: #1e3a8a;
            --sunn-secondary: #059669;
            --sunn-accent: #dc2626;
            --sunn-gold: #f59e0b;
            --sunn-light-blue: #dbeafe;
        }
        
        body {
            background: linear-gradient(135deg, var(--sunn-primary) 0%, var(--sunn-secondary) 100%);
            min-height: 100vh;
            font-family: '<PERSON><PERSON><PERSON> UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        
        .login-container {
            display: flex;
            align-items: center;
            justify-content: center;
            min-height: 100vh;
            padding: 20px;
        }

        .login-card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 12px;
            box-shadow: 0 12px 30px rgba(0, 0, 0, 0.1);
            border: none;
            overflow: hidden;
            max-width: 400px;
            width: 100%;
        }

        .university-header {
            background: linear-gradient(135deg, var(--sunn-primary) 0%, var(--sunn-secondary) 100%);
            color: white;
            text-align: center;
            padding: 2rem 1.5rem;
        }

        .university-logo {
            font-size: 3rem;
            margin-bottom: 0.5rem;
        }

        .university-title {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 0.25rem;
        }

        .university-subtitle {
            font-size: 0.9rem;
            opacity: 0.9;
            margin-bottom: 1rem;
        }

        .role-badge {
            background: rgba(255, 255, 255, 0.2);
            padding: 0.5rem 1rem;
            border-radius: 20px;
            font-size: 0.85rem;
            display: inline-block;
        }

        .login-form {
            padding: 2rem 1.5rem;
        }

        .form-control {
            border: 2px solid #e5e7eb;
            border-radius: 8px;
            padding: 0.75rem;
            font-size: 0.95rem;
            transition: all 0.3s ease;
        }

        .form-control:focus {
            border-color: var(--sunn-primary);
            box-shadow: 0 0 0 0.2rem rgba(30, 58, 138, 0.25);
        }

        .btn-login {
            background: linear-gradient(135deg, var(--sunn-primary) 0%, var(--sunn-secondary) 100%);
            border: none;
            border-radius: 8px;
            padding: 0.75rem;
            font-weight: 600;
            font-size: 1rem;
            color: white;
            width: 100%;
            transition: all 0.3s ease;
        }

        .btn-login:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(30, 58, 138, 0.3);
            color: white;
        }

        .login-links {
            text-align: center;
            padding: 1rem 1.5rem;
            background: #f8fafc;
            border-top: 1px solid #e5e7eb;
        }

        .login-links a {
            color: var(--sunn-primary);
            text-decoration: none;
            font-weight: 500;
            transition: color 0.3s ease;
        }

        .login-links a:hover {
            color: var(--sunn-secondary);
        }
    </style>
</head>
<body>
    <div class="login-container">
        <div class="login-card">
            <!-- University Header -->
            <div class="university-header">
                <div class="university-logo">
                    <i class="bi bi-person-badge"></i>
                </div>
                <h1 class="university-title">State University of Northern Negros</h1>
                <p class="university-subtitle">Employee Login Portal</p>
                <div class="role-badge">
                    <i class="bi bi-people me-1"></i>Faculty • Staff • Employees
                </div>
            </div>
            
            <!-- Login Form -->
            <div class="login-form">
                <form action="/employee/login" method="post">
                    <div class="mb-3">
                        <label for="employee_id" class="form-label">Employee ID</label>
                        <input type="text" class="form-control" id="employee_id" name="employee_id" placeholder="Enter your Employee ID" required>
                    </div>
                    <div class="mb-3">
                        <label for="password" class="form-label">Password</label>
                        <input type="password" class="form-control" id="password" name="password" placeholder="Enter your password" required>
                    </div>
                    <div class="mb-3 form-check">
                        <input type="checkbox" class="form-check-input" id="remember_me" name="remember_me">
                        <label class="form-check-label" for="remember_me">Remember me</label>
                    </div>
                    <button type="submit" class="btn btn-login">
                        <i class="bi bi-box-arrow-in-right me-2"></i>Login
                    </button>
                </form>
            </div>
            
            <!-- Login Links -->
            <div class="login-links">
                <div class="mb-2">
                    <a href="/">
                        <i class="bi bi-arrow-left me-1"></i>Back to Main Portal
                    </a>
                </div>
                <p class="mb-1">Don't have an account?</p>
                <a href="/employee/register">Register as New Employee</a>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
