<?php

// Define your application routes here
// Default home route - Portal Selection
$router->get('/', 'employee/HomeController@index');
$router->get('/home', 'employee/HomeController@index');

// Employee Portal Routes
$router->get('/employee/login', 'employee/EmployeeController@login');
$router->post('/employee/login', 'employee/EmployeeController@authenticate');
$router->get('/employee/register', 'employee/EmployeeController@register');
$router->post('/employee/register', 'employee/EmployeeController@processRegister');


// PDC Portal Routes
$router->get('/pdc/login', 'PdcController@login');
$router->post('/pdc/login', 'PdcController@authenticate');

// Admin/HR Portal Routes
$router->get('/admin/login', 'AdminController@login');
$router->post('/admin/login', 'AdminController@authenticate');

// Dashboard Routes (after login)
$router->get('/employee/dashboard', 'employee/EmployeeController@dashboard');
$router->get('/pdc/dashboard', 'PdcController@dashboard');
$router->get('/admin/dashboard', 'AdminController@dashboard');

// Logout Routes
$router->get('/logout', 'AuthController@logout');
