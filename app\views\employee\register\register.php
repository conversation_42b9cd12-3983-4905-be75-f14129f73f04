

<!doctype html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>Employee Registration - State University of Northern Negros</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.0/font/bootstrap-icons.css">
    <link rel="stylesheet" href="../dist/css/adminlte.css">
    <style>
        :root {
            --sunn-primary: #1e3a8a;
            --sunn-secondary: #059669;
            --sunn-accent: #dc2626;
            --sunn-gold: #f59e0b;
            --sunn-light-blue: #dbeafe;
        }
        
        body {
            background: linear-gradient(135deg, var(--sunn-primary) 0%, var(--sunn-secondary) 100%);
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            padding: 20px 0;
        }
        
        .register-container {
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 10px;
            min-height: 100vh;
            max-height: 100vh;
            overflow: hidden;
        }

        .register-card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 12px;
            box-shadow: 0 12px 30px rgba(0, 0, 0, 0.1);
            border: none;
            overflow: hidden;
            max-width: 480px;
            width: 100%;
            margin: auto;
            max-height: 95vh;
            overflow-y: auto;
        }
        
        .university-header {
            background: linear-gradient(135deg, var(--sunn-primary) 0%, var(--sunn-secondary) 100%);
            color: white;
            padding: 1rem 1rem;
            text-align: center;
        }

        .university-logo {
            width: 50px;
            height: 50px;
            background: white;
            border-radius: 50%;
            margin: 0 auto 0.75rem;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .university-logo i {
            font-size: 1.5rem;
            color: var(--sunn-primary);
        }

        .university-title {
            font-size: 1.1rem;
            font-weight: 700;
            margin: 0;
            line-height: 1.3;
        }

        .university-subtitle {
            font-size: 0.8rem;
            opacity: 0.9;
            margin: 0.25rem 0 0;
        }

        .register-form {
            padding: 1rem;
        }

        .section-title {
            color: var(--sunn-primary);
            font-weight: 600;
            font-size: 0.95rem;
            margin-bottom: 0.75rem;
            padding-bottom: 0.25rem;
            border-bottom: 2px solid var(--sunn-light-blue);
        }
        
        .form-control, .form-select {
            border: 2px solid #e5e7eb;
            border-radius: 6px;
            padding: 0.5rem;
            font-size: 0.9rem;
            transition: all 0.3s ease;
        }

        .form-control:focus, .form-select:focus {
            border-color: var(--sunn-primary);
            box-shadow: 0 0 0 0.2rem rgba(30, 58, 138, 0.25);
        }

        .btn-register {
            background: linear-gradient(135deg, var(--sunn-primary) 0%, var(--sunn-secondary) 100%);
            border: none;
            border-radius: 6px;
            padding: 0.6rem;
            font-weight: 600;
            font-size: 0.95rem;
            color: white;
            width: 100%;
            transition: all 0.3s ease;
        }
        
        .btn-register:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(30, 58, 138, 0.3);
            color: white;
        }

        .password-strength {
            height: 4px;
            border-radius: 2px;
            margin-top: 0.25rem;
            transition: all 0.3s ease;
        }
        
        .strength-weak { background-color: #ef4444; }
        .strength-medium { background-color: #f59e0b; }
        .strength-strong { background-color: #10b981; }
        
        .alert {
            border-radius: 10px;
            border: none;
        }
        
        .register-links {
            text-align: center;
            padding: 0.75rem 1rem 1rem;
            border-top: 1px solid #e5e7eb;
            font-size: 0.9rem;
        }

        .register-links a {
            color: var(--sunn-primary);
            text-decoration: none;
            font-weight: 500;
        }

        .register-links a:hover {
            color: var(--sunn-secondary);
        }

        .role-badge {
            background: var(--sunn-gold);
            color: white;
            padding: 0.3rem 0.6rem;
            border-radius: 12px;
            font-size: 0.7rem;
            font-weight: 600;
            display: inline-block;
            margin-top: 0.4rem;
        }
        
        @media (max-width: 768px) {
            .register-container {
                padding: 8px;
            }

            .register-card {
                max-width: 100%;
                margin: 0;
                max-height: 98vh;
            }

            .register-form {
                padding: 0.75rem;
            }

            .university-title {
                font-size: 1rem;
            }

            .university-subtitle {
                font-size: 0.75rem;
            }

            .university-header {
                padding: 0.75rem 0.75rem;
            }

            .section-title {
                font-size: 0.9rem;
            }

            .form-control, .form-select {
                font-size: 0.85rem;
                padding: 0.45rem;
            }

            .btn-register {
                font-size: 0.9rem;
                padding: 0.55rem;
            }

            .register-links {
                padding: 0.5rem 0.75rem 0.75rem;
                font-size: 0.85rem;
            }
        }
    </style>
</head>
<body>
    <div class="register-container">
        <div class="register-card">
            <!-- University Header -->
            <div class="university-header">
                <div class="university-logo">
                    <i class="bi bi-person-plus-fill"></i>
                </div>
                <h1 class="university-title">State University of Northern Negros</h1>
                <p class="university-subtitle">Employee Registration Portal</p>
                <div class="role-badge">
                    <i class="bi bi-person-add me-1"></i>New Employee Registration
                </div>
            </div>
            
            <!-- Registration Form -->
            <div class="register-form">
                <form action="../config/register_process.php" method="post" id="employeeRegisterForm">
                    
                    <!-- Account Information Section -->
                    <div class="mb-3">
                        <h6 class="section-title">
                            <i class="bi bi-key-fill me-2"></i>Account Information
                        </h6>
                        <div class="row">
                            <div class="col-md-6 mb-2">
                                <label for="employee_id" class="form-label">Employee ID </label>
                                <input type="text" class="form-control" id="employee_id" name="employee_id" placeholder="EMP-1234" required>
                                <div class="form-text">Format: EMP-XXXX (4-10 alphanumeric characters)</div>
                            </div>
                            <div class="col-md-6 mb-2">
                                <label for="department" class="form-label">Department/College *</label>
                                <select class="form-select" id="department" name="department" required>
                                    <option value="">Select Department/College</option>
                                    <option value="Office of the College President">Office of the College President</option>
                                    <option value="Office of the Vice President for Academic Affairs">Office of the Vice President for Academic Affairs</option>
                                    <option value="Office of the Vice President for Administration">Office of the Vice President for Administration</option>
                                    <option value="Office of the College Registrar">Office of the College Registrar</option>
                                    <option value="Guidance Center">Guidance Center</option>
                                    <option value="Bids and Awards Committee Office">Bids and Awards Committee Office</option>
                                    <option value="College Library">College Library</option>
                                    <option value="(CFAS)College of Fisheries and Allied Sciences">(CFAS) College of Fisheries and Allied Sciences</option>
                                    <option value="College of Arts and Sciences">College of Arts and Sciences</option>
                                    <option value="College of Business and Management">College of Business and Management</option>
                                    <option value="College of Education">College of Education</option>
                                    <option value="College of Engineering and Technology">College of Engineering and Technology</option>
                                    <option value="Research and Development Office">Research and Development Office</option>
                                    <option value="School Clinic">School Clinic</option>
                                    <option value="Student Affairs and Services Office">Student Affairs and Services Office</option>
                                    <option value="Supply Office">Supply Office</option>
                                    <option value="Supreme Student Council">Supreme Student Council</option>
                                    <option value="University Data Protection Office">University Data Protection Office</option>
                                </select>
                            </div>
                            <div class="col-md-6 mb-2">
                                <label for="employment_type" class="form-label">Employment Type *</label>
                                <select class="form-select" id="employment_type" name="employment_type" required>
                                    <option value="">Select Employment Type</option>
                                    <option value="Permanent">Permanent</option>
                                    <option value="Non-Permanent">Non-Permanent</option>
                                </select>
                            </div>

                            <div class="col-md-6 mb-2">
                                <label for="password" class="form-label">Password *</label>
                                <input type="password" class="form-control" id="password" name="password" required>
                                <div class="password-strength" id="password-strength"></div>
                                <div class="form-text">8+ chars with uppercase, lowercase, number, special char</div>
                            </div>
                            <div class="col-md-6 mb-2">
                                <label for="confirm_password" class="form-label">Confirm Password *</label>
                                <input type="password" class="form-control" id="confirm_password" name="confirm_password" required>
                                <div id="password-match" class="form-text"></div>
                            </div>
                        </div>
                    </div>

                    <!-- Terms and Conditions -->
                    <div class="mb-3">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="terms" name="terms" required>
                            <label class="form-check-label" for="terms">
                                I agree to the <a href="#" target="_blank">Terms and Conditions</a> and <a href="#" target="_blank">Privacy Policy</a> of SUNN *
                            </label>
                        </div>
                    </div>
                    
                    <button type="submit" class="btn btn-register">
                        <i class="bi bi-person-check me-2"></i>Register Employee Account
                    </button>
                </form>
            </div>
            
            <!-- Register Links -->
            <div class="register-links">
                <div class="mb-2">
                    <a href="../../index.html">
                        <i class="bi bi-arrow-left me-1"></i>Back to Main Portal
                    </a>
                </div>
                <p class="mb-1">Already have an account?</p>
                <div class="row">
                    <div class="col-4">
                        <a href="employee_login.html">Employee</a>
                    </div>
                    <div class="col-4">
                        <a href="pdc_login.html">PDC</a>
                    </div>
                    <div class="col-4">
                        <a href="admin_login.html">Admin</a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Form validation and submission handling
        document.getElementById('employeeRegisterForm').addEventListener('submit', function(e) {
            e.preventDefault();

            const form = this;
            const formData = new FormData(form);
            const submitBtn = form.querySelector('button[type="submit"]');
            const originalBtnText = submitBtn.innerHTML;

            // Show loading state
            submitBtn.innerHTML = '<i class="bi bi-hourglass-split me-2"></i>Registering...';
            submitBtn.disabled = true;

            // Remove any existing alerts
            const existingAlerts = document.querySelectorAll('.alert');
            existingAlerts.forEach(alert => alert.remove());

            fetch(form.action, {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // Show success message
                    const successAlert = document.createElement('div');
                    successAlert.className = 'alert alert-success mt-3';
                    successAlert.innerHTML = `
                        <i class="bi bi-check-circle me-2"></i>
                        ${data.message}
                    `;
                    form.appendChild(successAlert);

                    // Reset form
                    form.reset();

                    // Redirect to login page after 2 seconds
                    setTimeout(() => {
                        if (data.data && data.data.redirect) {
                            window.location.href = data.data.redirect;
                        }
                    }, 2000);
                } else {
                    // Show error message
                    const errorAlert = document.createElement('div');
                    errorAlert.className = 'alert alert-danger mt-3';
                    errorAlert.innerHTML = `
                        <i class="bi bi-exclamation-triangle me-2"></i>
                        ${data.message}
                    `;
                    form.appendChild(errorAlert);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                const errorAlert = document.createElement('div');
                errorAlert.className = 'alert alert-danger mt-3';
                errorAlert.innerHTML = `
                    <i class="bi bi-exclamation-triangle me-2"></i>
                    Registration failed. Please try again.
                `;
                form.appendChild(errorAlert);
            })
            .finally(() => {
                // Restore button state
                submitBtn.innerHTML = originalBtnText;
                submitBtn.disabled = false;
            });
        });

        // Password strength indicator
        document.getElementById('password').addEventListener('input', function() {
            const password = this.value;
            const strengthBar = document.getElementById('password-strength');

            let strength = 0;
            if (password.length >= 8) strength++;
            if (/[A-Z]/.test(password)) strength++;
            if (/[a-z]/.test(password)) strength++;
            if (/[0-9]/.test(password)) strength++;
            if (/[^A-Za-z0-9]/.test(password)) strength++;

            strengthBar.className = 'password-strength';
            if (strength <= 2) {
                strengthBar.classList.add('strength-weak');
            } else if (strength <= 4) {
                strengthBar.classList.add('strength-medium');
            } else {
                strengthBar.classList.add('strength-strong');
            }
        });

        // Password confirmation validation
        document.getElementById('confirm_password').addEventListener('input', function() {
            const password = document.getElementById('password').value;
            const confirmPassword = this.value;
            const matchDiv = document.getElementById('password-match');

            if (confirmPassword === '') {
                matchDiv.textContent = '';
                matchDiv.className = 'form-text';
            } else if (password === confirmPassword) {
                matchDiv.textContent = 'Passwords match';
                matchDiv.className = 'form-text text-success';
            } else {
                matchDiv.textContent = 'Passwords do not match';
                matchDiv.className = 'form-text text-danger';
            }
        });
    </script>
