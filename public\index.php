<?php

// Include the autoloader
require_once __DIR__ . '/../app/core/autoload.php';

// Include the bootstrap file
require_once __DIR__ . '/../app/bootload.php';

// Get the URL from the query parameter (set by .htaccess)
$url = $_GET['url'] ?? '/';

// Create and dispatch the router
$router = new App\Core\Router();

// Include routes
require_once __DIR__ . '/../app/routes/routes.php';
// Dispatch the request
$router->dispatch($url);