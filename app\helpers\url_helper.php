<?php

/**
 * Generate a URL for the application
 * @param string $path The path to append to the base URL
 * @return string The full URL
 */
function url($path = '') {
    $baseUrl = rtrim(BASE_URL, '/');
    $path = ltrim($path, '/');
    return $baseUrl . '/' . $path;
}

/**
 * Generate a URL relative to the current request
 * @param string $path The path to append
 * @return string The relative URL
 */
function asset($path = '') {
    // For assets, we might want to use a different base
    return url($path);
}
