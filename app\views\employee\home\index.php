<!doctype html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>Login Portal - State University of Northern Negros</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.0/font/bootstrap-icons.css">
    <style>
        :root {
            --sunn-primary: #1e3a8a;
            --sunn-secondary: #059669;
            --sunn-accent: #dc2626;
            --sunn-gold: #f59e0b;
            --sunn-light-blue: #dbeafe;
        }

        body {
            background: linear-gradient(135deg, var(--sunn-primary) 0%, var(--sunn-secondary) 100%);
            min-height: 100vh;
            font-family: '<PERSON><PERSON><PERSON> UI', Tahoma, Geneva, Verdana, sans-serif;
        }

        .portal-container {
            min-height: 100vh;
            max-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 15px;
            overflow: hidden;
        }

        .portal-card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
            border: none;
            overflow: hidden;
            max-width: 850px;
            width: 100%;
            max-height: 95vh;
            overflow-y: auto;
        }

        .university-header {
            background: linear-gradient(135deg, var(--sunn-primary) 0%, var(--sunn-secondary) 100%);
            color: white;
            padding: 1.5rem 1.5rem;
            text-align: center;
            position: relative;
        }

        .university-logo {
            width: 70px;
            height: 70px;
            background: white;
            border-radius: 50%;
            margin: 0 auto 1rem;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .university-logo i {
            font-size: 2.2rem;
            color: var(--sunn-primary);
        }

        .university-title {
            font-size: 1.5rem;
            font-weight: 700;
            margin: 0;
            line-height: 1.3;
        }

        .university-subtitle {
            font-size: 0.95rem;
            opacity: 0.9;
            margin: 0.25rem 0 0;
        }

        .portal-content {
            padding: 1.5rem 1.5rem;
        }

        .portal-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));
            gap: 1.25rem;
            margin-top: 1rem;
        }

        .portal-option {
            background: white;
            border: 2px solid #e5e7eb;
            border-radius: 12px;
            padding: 1.25rem;
            text-align: center;
            text-decoration: none;
            color: inherit;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .portal-option:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 30px rgba(0, 0, 0, 0.1);
            border-color: var(--sunn-primary);
            color: inherit;
            text-decoration: none;
        }

        .portal-option::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
            transition: left 0.5s;
        }

        .portal-option:hover::before {
            left: 100%;
        }

        .portal-icon {
            width: 60px;
            height: 60px;
            margin: 0 auto 0.75rem;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.8rem;
            color: white;
            position: relative;
            z-index: 1;
        }

        .employee-portal .portal-icon {
            background: linear-gradient(135deg, var(--sunn-secondary) 0%, #047857 100%);
        }

        .pdc-portal .portal-icon {
            background: linear-gradient(135deg, #7c3aed 0%, #a855f7 100%);
        }

        .admin-portal .portal-icon {
            background: linear-gradient(135deg, var(--sunn-accent) 0%, #b91c1c 100%);
        }

        .portal-title {
            font-size: 1.1rem;
            font-weight: 600;
            margin-bottom: 0.4rem;
            color: var(--sunn-primary);
        }

        .portal-description {
            color: #6b7280;
            font-size: 0.8rem;
            line-height: 1.4;
        }

        .portal-badge {
            position: absolute;
            top: 1rem;
            right: 1rem;
            background: var(--sunn-gold);
            color: white;
            padding: 0.25rem 0.75rem;
            border-radius: 15px;
            font-size: 0.7rem;
            font-weight: 600;
        }

        .footer-info {
            text-align: center;
            padding: 1rem;
            border-top: 1px solid #e5e7eb;
            background: #f9fafb;
        }

        .footer-info p {
            margin: 0;
            color: #6b7280;
            font-size: 0.8rem;
        }

        @media (max-width: 768px) {
            .portal-container {
                padding: 10px;
            }

            .portal-card {
                max-width: 100%;
                max-height: 98vh;
            }

            .university-title {
                font-size: 1.3rem;
            }

            .university-subtitle {
                font-size: 0.85rem;
            }

            .portal-content {
                padding: 1.25rem 1rem;
            }

            .portal-grid {
                grid-template-columns: 1fr;
                gap: 1rem;
            }

            .portal-option {
                padding: 1rem;
            }

            .university-header {
                padding: 1.25rem 1rem;
            }
        }
    </style>
</head>
<body>
    <div class="portal-container">
        <div class="portal-card">
            <!-- University Header -->
            <div class="university-header">
                <div class="university-logo">
                    <i class="bi bi-mortarboard-fill"></i>
                </div>
                <h1 class="university-title">State University of Northern Negros</h1>
                <p class="university-subtitle">Unified Login Portal</p>
            </div>

            <!-- Portal Content -->
            <div class="portal-content">
                <div class="text-center mb-3">
                    <h4 class="mb-1">Welcome to SUNN Portal</h4>
                    <p class="text-muted mb-0">Select your login portal to access your account</p>
                </div>

                <div class="portal-grid">
                    <!-- Employee Portal -->
                    <a href="/employee/login" class="portal-option employee-portal">
                        <div class="portal-badge">Most Used</div>
                        <div class="portal-icon">
                            <i class="bi bi-person-badge"></i>
                        </div>
                        <h4 class="portal-title">Employee Portal</h4>
                        <p class="portal-description">
                            Access for faculty, staff, and general employees.
                        </p>
                        <div class="mt-3">
                            <small class="text-muted">
                                <i class="bi bi-people me-1"></i>Faculty • Staff • Employees
                            </small>
                        </div>
                    </a>

                    <!-- PDC Portal -->
                    <a href="/pdc/login" class="portal-option pdc-portal">
                        <div class="portal-icon">
                            <i class="bi bi-diagram-3"></i>
                        </div>
                        <h4 class="portal-title">PDC Portal</h4>
                        <p class="portal-description">
                            Program Development Committee
                        </p>
                        <div class="mt-3">
                            <small class="text-muted">
                                <i class="bi bi-gear me-1"></i>Academic Administration
                            </small>
                        </div>
                    </a>

                    <!-- Admin/HR Portal -->
                    <a href="/admin/login" class="portal-option admin-portal">
                        <div class="portal-badge">Secure</div>
                        <div class="portal-icon">
                            <i class="bi bi-shield-fill-check"></i>
                        </div>
                        <h4 class="portal-title">Admin/HR Portal</h4>
                        <p class="portal-description">
                            Administrative access for system administrators, HR personnel,
                            and other authorized administrative staff.
                        </p>
                        <div class="mt-3">
                            <small class="text-muted">
                                <i class="bi bi-shield-lock me-1"></i>Restricted Access
                            </small>
                        </div>
                    </a>
                </div>

                <!-- Registration Link -->
                <div class="text-center mt-3">
                    <div class="border-top pt-3">
                        <h6 class="mb-2">New Employee?</h6>
                        <a href="/employee/register" class="btn btn-outline-primary">
                            <i class="bi bi-person-plus me-1"></i>Register Account
                        </a>
                        <p class="text-muted mt-1 mb-0">
                            <small>Registration requires HR approval</small>
                        </p>
                    </div>
                </div>
            </div>

            <!-- Footer Info -->
            <div class="footer-info">
                <p>
                    <i class="bi bi-telephone me-1"></i>
                    Support: ************ •
                    <i class="bi bi-envelope me-1"></i>
                    sunn.edu.ph
                </p>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Add some interactive effects
        document.addEventListener('DOMContentLoaded', function() {
            const portalOptions = document.querySelectorAll('.portal-option');

            portalOptions.forEach(option => {
                option.addEventListener('mouseenter', function() {
                    this.style.transform = 'translateY(-5px) scale(1.02)';
                });

                option.addEventListener('mouseleave', function() {
                    this.style.transform = 'translateY(0) scale(1)';
                });
            });
        });
    </script>
</body>
</html>
